#!/usr/bin/env python3
"""
Script to show actual data content from Parquet files
Helps verify that data was correctly read from databases
"""

import sys
import os

# Add src/main to path
sys.path.append('src/main')

from config.spark_session_builder import SparkSessionBuilder, setup_environment


def show_parquet_data():
    """Show actual data content from Parquet files"""
    
    print("🔍 Reading Parquet data content...")
    
    # Setup environment
    setup_environment()
    
    # Create Spark session
    spark = SparkSessionBuilder.create_iceberg_session("DataViewer", "WARN")
    print("✅ Spark session created")
    
    # Paths to check
    paths_to_check = [
        "s3a://mock-bucket/mysql_customers/",
        "s3a://mock-bucket/mssql_customers/",
        "s3a://mock-bucket/mysql_data/customers/",  # Fallback paths
        "s3a://mock-bucket/mssql_data/customers/"
    ]
    
    for path in paths_to_check:
        try:
            print(f"\n📊 Checking: {path}")
            
            # Try to read Parquet files
            df = spark.read.parquet(path)
            count = df.count()
            
            if count > 0:
                print(f"  ✅ Found {count} records")
                print(f"  📋 Schema:")
                for field in df.schema.fields:
                    print(f"    - {field.name}: {field.dataType}")
                
                print(f"  📄 Sample data (first 5 records):")
                sample_data = df.limit(5).collect()
                for i, row in enumerate(sample_data, 1):
                    row_dict = row.asDict()
                    print(f"    {i}. {row_dict}")
                
                # Show partitions if any
                try:
                    partitions = df.select("age_range").distinct().collect()
                    if partitions:
                        partition_values = [row.age_range for row in partitions]
                        print(f"  🗂️  Partitions: {partition_values}")
                except:
                    pass
                    
            else:
                print(f"  ⚠️  Path exists but no data found")
                
        except Exception as e:
            if "Path does not exist" in str(e):
                print(f"  ❌ Path not found: {path}")
            else:
                print(f"  ❌ Error reading {path}: {e}")
    
    spark.stop()
    print("\n✅ Data content check completed!")


if __name__ == "__main__":
    show_parquet_data()

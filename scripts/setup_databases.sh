#!/bin/bash

# Setup databases with sample data for the pipeline
# This script creates databases, tables, and inserts sample data

set -e

echo "🗄️  Setting up databases with sample data..."

# Wait for databases to be ready
echo "⏳ Waiting for databases to start..."
sleep 10

# MySQL Setup
echo "📊 Setting up MySQL database..."
docker exec mysql mysql -u test -p123456 -e "
CREATE DATABASE IF NOT EXISTS test;
USE test;

-- Create customers table
CREATE TABLE IF NOT EXISTS customers (
    id INT PRIMARY KEY,
    name VARCHAR(100),
    age INT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert sample data
INSERT INTO customers (id, name, age, updated_at) VALUES
(1, '<PERSON>', 25, '2025-01-03 12:00:00'),
(2, '<PERSON>', 35, '2025-01-03 12:00:00'),
(3, '<PERSON>', 45, '2025-01-03 12:00:00'),
(4, '<PERSON>', 28, '2025-01-03 12:00:00'),
(5, '<PERSON>', 52, '2025-01-03 12:00:00')
ON DUPLICATE KEY UPDATE 
    name = VALUES(name), 
    age = VALUES(age), 
    updated_at = VALUES(updated_at);

-- Show data
SELECT 'MySQL customers:' as info;
SELECT * FROM customers;
"

# SQL Server Setup
echo "📊 Setting up SQL Server database..."
docker exec sqlserver /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P '123456aA' -C -Q "
-- Create database
IF NOT EXISTS (SELECT * FROM sys.databases WHERE name = 'test')
    CREATE DATABASE test;

USE test;

-- Create customers table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'customers')
    CREATE TABLE customers (
        id INT PRIMARY KEY,
        name NVARCHAR(100),
        age INT,
        updated_at DATETIME DEFAULT GETDATE()
    );

-- Insert/Update sample data
MERGE customers AS target
USING (VALUES 
    (1, 'John Doe', 25, '2025-01-03 12:00:00'),
    (2, 'Jane Smith', 35, '2025-01-03 12:00:00'),
    (3, 'Bob Johnson', 45, '2025-01-03 12:00:00'),
    (4, 'Alice Brown', 28, '2025-01-03 12:00:00'),
    (5, 'Charlie Wilson', 52, '2025-01-03 12:00:00')
) AS source (id, name, age, updated_at)
ON target.id = source.id
WHEN MATCHED THEN 
    UPDATE SET name = source.name, age = source.age, updated_at = source.updated_at
WHEN NOT MATCHED THEN 
    INSERT (id, name, age, updated_at) VALUES (source.id, source.name, source.age, source.updated_at);

-- Show data
SELECT 'SQL Server customers:' as info, COUNT(*) as total_records FROM customers;
SELECT * FROM customers;
"

# PostgreSQL Setup (for job logging)
echo "📊 Setting up PostgreSQL for job logging..."
docker exec postgres psql -U postgres -d test -c "
-- Create job logger table
CREATE TABLE IF NOT EXISTS job_logger (
    id SERIAL PRIMARY KEY,
    system_name VARCHAR(100),
    schema_name VARCHAR(100),
    table_name VARCHAR(100),
    lower_bound TIMESTAMP,
    upper_bound TIMESTAMP,
    record_count INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Show existing logs
SELECT 'Job logger records:' as info, COUNT(*) as total_logs FROM job_logger;
"

# LocalStack S3 Setup
echo "📦 Setting up S3 bucket..."
curl -X PUT http://localhost:4566/mock-bucket 2>/dev/null || echo "Bucket already exists"

echo ""
echo "✅ Database setup completed!"
echo ""
echo "📋 Summary:"
echo "  • MySQL: 5 customer records in test.customers"
echo "  • SQL Server: 5 customer records in test.customers"  
echo "  • PostgreSQL: job_logger table ready"
echo "  • S3: mock-bucket created"
echo ""
echo "🚀 Ready to run pipeline!"
echo "   ./scripts/run_pipeline.sh iceberg"

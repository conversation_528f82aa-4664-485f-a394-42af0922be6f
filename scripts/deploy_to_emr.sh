#!/bin/bash

# Deploy and run pipeline on AWS EMR
# Usage: ./scripts/deploy_to_emr.sh

set -e

echo "🚀 Deploying Pipeline to AWS EMR..."

# Configuration
EMR_CLUSTER_ID=${EMR_CLUSTER_ID:-"j-XXXXXXXXXX"}  # Set your cluster ID
S3_CODE_BUCKET=${S3_CODE_BUCKET:-"your-code-bucket"}
S3_DATA_BUCKET=${S3_DATA_BUCKET:-"your-data-bucket"}
AWS_REGION=${AWS_REGION:-"us-east-1"}

# Validate required environment variables
if [ "$EMR_CLUSTER_ID" = "j-XXXXXXXXXX" ]; then
    echo "❌ Please set EMR_CLUSTER_ID environment variable"
    echo "   Example: export EMR_CLUSTER_ID=j-1234567890ABC"
    exit 1
fi

if [ "$S3_CODE_BUCKET" = "your-code-bucket" ]; then
    echo "❌ Please set S3_CODE_BUCKET environment variable"
    echo "   Example: export S3_CODE_BUCKET=my-pipeline-code"
    exit 1
fi

echo "📋 Configuration:"
echo "  EMR Cluster ID: $EMR_CLUSTER_ID"
echo "  S3 Code Bucket: $S3_CODE_BUCKET"
echo "  S3 Data Bucket: $S3_DATA_BUCKET"
echo "  AWS Region: $AWS_REGION"
echo ""

# Step 1: Package and upload code
echo "📦 Step 1: Packaging code..."
cd src/main
zip -r ../../pipeline-code.zip . -x "**/__pycache__/*" "**/*.pyc"
cd ../..

echo "📤 Step 2: Uploading to S3..."
aws s3 cp pipeline-code.zip s3://$S3_CODE_BUCKET/pipeline-code.zip --region $AWS_REGION
aws s3 cp scripts/run_emr_job.py s3://$S3_CODE_BUCKET/run_emr_job.py --region $AWS_REGION

# Step 3: Submit EMR step
echo "🎯 Step 3: Submitting EMR job..."
aws emr add-steps \
    --cluster-id $EMR_CLUSTER_ID \
    --region $AWS_REGION \
    --steps '[
        {
            "Name": "Database-to-Parquet-Pipeline",
            "ActionOnFailure": "CONTINUE",
            "HadoopJarStep": {
                "Jar": "command-runner.jar",
                "Args": [
                    "spark-submit",
                    "--deploy-mode", "cluster",
                    "--py-files", "s3://'$S3_CODE_BUCKET'/pipeline-code.zip",
                    "s3://'$S3_CODE_BUCKET'/run_emr_job.py",
                    "--config_file", "job.aws.parquet.yaml",
                    "--s3_data_bucket", "'$S3_DATA_BUCKET'"
                ]
            }
        }
    ]'

echo "✅ Pipeline submitted to EMR!"
echo ""
echo "🔍 To monitor progress:"
echo "  aws emr list-steps --cluster-id $EMR_CLUSTER_ID --region $AWS_REGION"
echo ""
echo "📊 Your Parquet files will be saved to:"
echo "  s3://$S3_DATA_BUCKET/mysql_customers/"
echo "  s3://$S3_DATA_BUCKET/mssql_customers/"

# Cleanup
rm -f pipeline-code.zip

#!/usr/bin/env python3
"""
EMR Job Runner for Database to Parquet Pipeline
Runs on EMR cluster to execute the pipeline
"""

import sys
import os
import argparse
from pyspark.sql import SparkSession

# Add current directory to path for imports
sys.path.insert(0, os.getcwd())

from pipeline_main import main as pipeline_main


def setup_emr_environment(s3_data_bucket):
    """Setup environment variables for EMR execution"""
    
    # Set configuration paths
    os.environ['CONFIG_ROOT_DIR'] = 'resources'
    os.environ['CONFIG_ENDPOINTS_DIR'] = 'resources/endpoints'
    os.environ['CONFIG_JOBS_DIR'] = 'resources/jobs'
    os.environ['EXTRA_JARS_FOLDER'] = 'resources/jars'
    
    # AWS configuration (EMR provides these automatically)
    os.environ['AWS_DEFAULT_REGION'] = os.environ.get('AWS_DEFAULT_REGION', 'us-east-1')
    
    # S3 configuration for production
    os.environ['S3_DATA_BUCKET'] = s3_data_bucket
    
    print(f"✅ EMR Environment configured for bucket: {s3_data_bucket}")


def main():
    """Main EMR job execution function"""
    
    parser = argparse.ArgumentParser(description='Run Database to Parquet Pipeline on EMR')
    parser.add_argument('--config_file', required=True, help='Job configuration file')
    parser.add_argument('--s3_data_bucket', required=True, help='S3 bucket for output data')
    
    args = parser.parse_args()
    
    print("🚀 Starting Database to Parquet Pipeline on EMR...")
    print(f"📋 Config file: {args.config_file}")
    print(f"📦 Output bucket: s3://{args.s3_data_bucket}/")
    
    # Setup EMR environment
    setup_emr_environment(args.s3_data_bucket)
    
    # Prepare pipeline arguments
    pipeline_args = [
        '--config_file', args.config_file,
        '--config_source_type', 'local',
        '--config_root_dir', 'resources',
        '--app_type', 'batch',
        '--s3_type', 'aws',
        '--lowerbound', '1970-01-01',
        '--upperbound', '2030-12-31',
        '--report_date', '2024-01-01',
        '--pipeline', 'production',
        '--batch_size', '10000',
        '--custom', '{}',
        '--predicate', '',
        '--job_id', f'emr-pipeline-{args.config_file.replace(".yaml", "")}',
        '--extra_jars_folder', 'resources/jars'
    ]
    
    # Override sys.argv for pipeline_main
    original_argv = sys.argv
    sys.argv = ['pipeline_main.py'] + pipeline_args
    
    try:
        # Run the pipeline
        print("🔄 Executing pipeline...")
        pipeline_main()
        print("✅ Pipeline completed successfully!")
        
    except Exception as e:
        print(f"❌ Pipeline failed: {e}")
        raise
        
    finally:
        # Restore original argv
        sys.argv = original_argv


if __name__ == "__main__":
    main()

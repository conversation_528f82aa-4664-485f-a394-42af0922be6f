#!/bin/bash

# <PERSON>ript to check Iceberg data in LocalStack S3

echo "🔍 Checking Iceberg data storage..."
echo ""

# Check if LocalStack is running
if ! curl -s http://localhost:4566/health > /dev/null; then
    echo "❌ LocalStack is not running. Please start it with: docker-compose up -d"
    exit 1
fi

echo "✅ LocalStack is running"
echo ""

# List all Iceberg files
echo "📁 Iceberg warehouse structure:"
echo "s3://mock-bucket/iceberg-warehouse/"

# Get the S3 listing and format it nicely
curl -s "http://localhost:4566/mock-bucket?list-type=2&prefix=iceberg-warehouse/" | \
grep -o '<Key>[^<]*</Key>' | \
sed 's/<Key>//g; s/<\/Key>//g' | \
sort | \
while read -r key; do
    # Extract the path components
    if [[ $key == *"/metadata/"* ]]; then
        echo "  📄 $key"
    elif [[ $key == *"/data/"* ]]; then
        echo "  💾 $key"
    else
        echo "  📂 $key"
    fi
done

echo ""
echo "🏷️  Simple Parquet Files Found:"

# Extract simple parquet directories
curl -s "http://localhost:4566/mock-bucket?list-type=2" | \
grep -o '<Key>[^<]*_data/[^<]*</Key>' | \
sed 's/<Key>//g; s/<\/Key>//g' | \
grep "_SUCCESS" | \
sed 's/_SUCCESS//g' | \
sort -u | \
while read -r path; do
    if [ ! -z "$path" ]; then
        echo "  ✅ s3://mock-bucket/$path (Parquet files)"
    fi
done

echo ""
echo "💡 These are simple Parquet files organized by database/table structure."
echo "   Example: s3://mock-bucket/mysql_data/customers/ contains MySQL customer data"
echo "   Example: s3://mock-bucket/mssql_data/customers/ contains SQL Server customer data"

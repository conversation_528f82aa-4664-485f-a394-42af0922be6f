#!/bin/bash

# Script to check Iceberg data in LocalStack S3

echo "🔍 Checking Iceberg data storage..."
echo ""

# Check if LocalStack is running
if ! curl -s http://localhost:4566/health > /dev/null; then
    echo "❌ LocalStack is not running. Please start it with: docker-compose up -d"
    exit 1
fi

echo "✅ LocalStack is running"
echo ""

# List all Iceberg files
echo "📁 Iceberg warehouse structure:"
echo "s3://mock-bucket/iceberg-warehouse/"

# Get the S3 listing and format it nicely
curl -s "http://localhost:4566/mock-bucket?list-type=2&prefix=iceberg-warehouse/" | \
grep -o '<Key>[^<]*</Key>' | \
sed 's/<Key>//g; s/<\/Key>//g' | \
sort | \
while read -r key; do
    # Extract the path components
    if [[ $key == *"/metadata/"* ]]; then
        echo "  📄 $key"
    elif [[ $key == *"/data/"* ]]; then
        echo "  💾 $key"
    else
        echo "  📂 $key"
    fi
done

echo ""
echo "🏷️  Iceberg Tables Found:"

# Extract unique table names
curl -s "http://localhost:4566/mock-bucket?list-type=2&prefix=iceberg-warehouse/lakehouse/" | \
grep -o '<Key>iceberg-warehouse/lakehouse/[^/]*/</Key>' | \
sed 's/<Key>iceberg-warehouse\/lakehouse\///g; s/<\/Key>//g; s/\///g' | \
sort -u | \
while read -r table; do
    if [ ! -z "$table" ]; then
        echo "  ✅ iceberg_catalog.lakehouse.$table"
    fi
done

echo ""
echo "💡 To query these tables, you can use Spark SQL with the iceberg_catalog."
echo "   Example: SELECT * FROM iceberg_catalog.lakehouse.mysql_customers LIMIT 10"

#!/usr/bin/env python3
"""
Test script for Apache Iceberg integration
Runs the Iceberg pipeline and verifies data output
"""

from writer.factory_writer import WriterFactory
from transformer.factory_transformer import TransformerFactory
from reader.factory_reader import ReaderFactory
from utils.commons import Pipeline
from config.pipeline import PipelineListConfig
from config.configparser import load_yaml
from utils.file_util import read_file
from config.environment import env_config
import os
import sys
import logging
from pyspark.sql import SparkSession

# Add src to path
sys.path.append('src/main')


def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def create_spark_session():
    """Create Spark session with Iceberg support"""
    print("Creating Spark session with Iceberg support...")

    # Get Spark configuration
    spark_config = env_config.get_spark_config()

    # Add Iceberg warehouse location
    spark_config["spark.sql.catalog.spark_catalog.warehouse"] = "s3a://mock-bucket/iceberg-warehouse/"

    # Create Spark session
    builder = SparkSession.builder.appName("IcebergPipelineTest")

    # Apply configurations
    for key, value in spark_config.items():
        builder = builder.config(key, value)

    spark = builder.getOrCreate()

    # Set log level
    spark.sparkContext.setLogLevel("WARN")

    print("✅ Spark session created successfully")
    return spark


def run_iceberg_pipeline():
    """Run the Iceberg pipeline"""
    print("\n🚀 Starting Iceberg Pipeline Test...")

    # Setup environment
    os.environ["CONFIG_ROOT_DIR"] = "src/main/resources"
    os.environ["CONFIG_ENDPOINTS_DIR"] = "src/main/resources/endpoints"
    os.environ["CONFIG_JOBS_DIR"] = "src/main/resources/jobs"
    os.environ["EXTRA_JARS_FOLDER"] = "src/main/resources/jars"

    # Create Spark session
    spark = create_spark_session()

    try:
        # Load pipeline configuration
        print("📋 Loading Iceberg pipeline configuration...")
        config_content = read_file(
            "src/main/resources/jobs/job.iceberg.sample.yaml")
        pipeline_config = load_yaml(config_content, PipelineListConfig)

        print(f"✅ Loaded {len(pipeline_config.jobs)} jobs")

        # Run pipeline
        print("\n🔄 Executing Iceberg pipeline...")

        for i, job_config in enumerate(pipeline_config.jobs, 1):
            print(
                f"\n--- Job {i}/{len(pipeline_config.jobs)}: {job_config.name} ---")

            try:
                # Create pipeline components
                reader = ReaderFactory().create(job_config.readers)
                transformers = []
                if job_config.transformers:
                    for transformer_config in job_config.transformers:
                        transformers.append(
                            TransformerFactory().create(transformer_config))
                writer = WriterFactory().create(job_config.writers)

                # Execute pipeline
                df = reader.read(spark)
                print(f"Read {df.count()} records from source")

                # Apply transformations
                for transformer in transformers:
                    df = transformer.transform(df)
                    print(
                        f"Applied transformation: {type(transformer).__name__}")

                # Write data
                writer.write(df)
                print(f"✅ Job {job_config.name} completed successfully")

            except Exception as e:
                print(f"❌ Job {job_config.name} failed: {str(e)}")
                raise

        print("\n🎉 All Iceberg jobs completed successfully!")

        # Verify Iceberg tables
        verify_iceberg_tables(spark)

    except Exception as e:
        print(f"❌ Pipeline failed: {str(e)}")
        raise
    finally:
        spark.stop()


def verify_iceberg_tables(spark):
    """Verify Iceberg tables were created and contain data"""
    print("\n🔍 Verifying Iceberg tables...")

    tables = [
        "spark_catalog.lakehouse.mysql_customers",
        "spark_catalog.lakehouse.mssql_customers",
        "spark_catalog.lakehouse.s3_customers"
    ]

    for table in tables:
        try:
            print(f"\n📊 Checking table: {table}")

            # Check if table exists
            df = spark.sql(f"SELECT * FROM {table}")
            count = df.count()

            print(f"✅ Table {table} exists with {count} records")

            # Show sample data
            if count > 0:
                print("Sample data:")
                df.show(5, truncate=False)

                # Show table schema
                print("Table schema:")
                df.printSchema()

                # Show partitions if any
                try:
                    partitions_df = spark.sql(f"SHOW PARTITIONS {table}")
                    partition_count = partitions_df.count()
                    if partition_count > 0:
                        print(f"Partitions ({partition_count}):")
                        partitions_df.show(10, truncate=False)
                except:
                    print("No partitions or partitioning info not available")

        except Exception as e:
            print(f"❌ Error checking table {table}: {str(e)}")


def check_iceberg_files():
    """Check Iceberg files in S3"""
    print("\n📁 Checking Iceberg files in S3...")

    import subprocess

    try:
        # List Iceberg warehouse contents
        result = subprocess.run([
            "curl", "-s", "http://localhost:4566/mock-bucket?list-type=2&prefix=iceberg-warehouse/"
        ], capture_output=True, text=True)

        if result.returncode == 0:
            print("Iceberg warehouse contents:")
            # Parse and display S3 listing
            import xml.etree.ElementTree as ET
            root = ET.fromstring(result.stdout)

            for content in root.findall('.//{http://s3.amazonaws.com/doc/2006-03-01/}Contents'):
                key = content.find(
                    '{http://s3.amazonaws.com/doc/2006-03-01/}Key')
                size = content.find(
                    '{http://s3.amazonaws.com/doc/2006-03-01/}Size')
                if key is not None and size is not None:
                    print(f"  {key.text} ({size.text} bytes)")
        else:
            print("❌ Failed to list S3 contents")

    except Exception as e:
        print(f"❌ Error checking S3 files: {str(e)}")


if __name__ == "__main__":
    setup_logging()

    print("🧊 Apache Iceberg Pipeline Test")
    print("=" * 50)

    try:
        run_iceberg_pipeline()
        check_iceberg_files()

        print("\n🎉 Iceberg pipeline test completed successfully!")
        print("\n📋 Summary:")
        print("✅ Iceberg JAR files downloaded")
        print("✅ Spark session configured with Iceberg")
        print("✅ Pipeline executed successfully")
        print("✅ Iceberg tables created with Parquet format")
        print("✅ Data partitioned and stored in S3")

    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        sys.exit(1)

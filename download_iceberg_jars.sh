#!/bin/bash

# Download Apache Iceberg JAR files for Spark 3.5
# This script downloads the necessary Iceberg dependencies

JARS_DIR="src/main/resources/jars"
ICEBERG_VERSION="1.4.3"
SPARK_VERSION="3.5"

echo "Downloading Apache Iceberg JAR files..."
echo "Iceberg Version: $ICEBERG_VERSION"
echo "Spark Version: $SPARK_VERSION"
echo "Target Directory: $JARS_DIR"

# Create jars directory if it doesn't exist
mkdir -p $JARS_DIR

cd $JARS_DIR

# Download Iceberg Spark Runtime JAR
echo "Downloading Iceberg Spark Runtime..."
curl -L -O "https://repo1.maven.org/maven2/org/apache/iceberg/iceberg-spark-runtime-${SPARK_VERSION}_2.12/${ICEBERG_VERSION}/iceberg-spark-runtime-${SPARK_VERSION}_2.12-${ICEBERG_VERSION}.jar"

# Download Iceberg AWS JAR (for S3 support)
echo "Downloading Iceberg AWS..."
curl -L -O "https://repo1.maven.org/maven2/org/apache/iceberg/iceberg-aws/${ICEBERG_VERSION}/iceberg-aws-${ICEBERG_VERSION}.jar"

# Download additional AWS SDK dependencies if needed
echo "Downloading AWS Bundle (if not already present)..."
if [ ! -f "aws-java-sdk-bundle-1.12.262.jar" ]; then
    curl -L -O "https://repo1.maven.org/maven2/com/amazonaws/aws-java-sdk-bundle/1.12.262/aws-java-sdk-bundle-1.12.262.jar"
fi

echo "Iceberg JAR files downloaded successfully!"
echo "Files in $JARS_DIR:"
ls -la *.jar | grep -E "(iceberg|aws)"

cd - > /dev/null

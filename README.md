# Simple Database to Parquet Pipeline

Read data from MySQL/SQL Server databases and write to S3 as Parquet files.

## 🎯 What It Does

- **Reads**: MySQL and SQL Server customer data
- **Transforms**: Adds age_range column (young/mature)
- **Writes**: Parquet files to S3 with Snappy compression
- **Partitions**: By age_range for better performance

## 🚀 Quick Start

```bash
# 1. Download dependencies
./scripts/download_jars.sh

# 2. Start services
source scripts/setup_environment.sh && docker-compose up -d

# 3. Run pipeline (choose one)
./scripts/run_pipeline.sh iceberg

# OR run manually
python3 src/main/pipeline_main.py \
  --config_file job.iceberg.sample.yaml \
  --config_source_type local \
  --config_root_dir src/main/resources \
  --app_type batch \
  --s3_type localstack \
  --s3_url http://localhost:4566 \
  --extra_jars_folder src/main/resources/jars

# 4. Check results - view your data
python3 scripts/show_data.py
```

## 📁 Output Structure

Your data is saved as Parquet files in S3:

```
s3://mock-bucket/
├── mysql_customers/
│   └── age_range=young/
│       └── part-*.snappy.parquet    # MySQL customer data
└── mssql_customers/
    └── age_range=mature/
        └── part-*.snappy.parquet    # SQL Server customer data
```

**Partition Field**: `age_range` (young/mature based on age 20-40)

## ⚙️ Configuration

Simple job configuration in `src/main/resources/jobs/job.iceberg.sample.yaml`:

```yaml
jobs:
  - name: mysql_to_parquet
    readers:
      type: MysqlReader
      endpoint: mysql.endpoint.yaml
      table: customers
    transformers:
      - type: WithColumnTransformer
        columns:
          - colname: age_range
            expr: CASE WHEN age between 20 and 40 THEN 'young' ELSE 'mature' END
    writers:
      type: IcebergWriter
      endpoint: s3.localstack.endpoint.yaml
      s3_path: mysql_customers
      partition_columns:
        - age_range
```

## 🔧 Helper Scripts

All scripts are in the `scripts/` directory:

- `download_jars.sh` - Download required JAR files
- `setup_environment.sh` - Set environment variables
- `run_pipeline.sh iceberg` - Run the pipeline
- `show_data.py` - View your data as clean dataframes

## 📊 Data Flow

1. **MySQL/SQL Server** → Read customer data
2. **Transform** → Add age_range column (young: 20-40, mature: >40)
3. **Partition** → Group by age_range
4. **Write** → Save as Parquet files to S3

## 🗂️ Project Structure

```
spark-metadata-driven/
├── scripts/                     # Helper scripts
├── src/main/
│   ├── writer/
│   │   └── iceberg_writer.py    # Simple Parquet writer
│   ├── resources/
│   │   ├── jobs/
│   │   │   └── job.iceberg.sample.yaml
│   │   └── jars/               # Downloaded JAR files
└── docker-compose.yml          # Local databases
```

## ✅ What You Get

- **Clean Parquet files** with Snappy compression
- **Partitioned data** for better query performance  
- **Simple S3 structure** - easy to understand
- **No complex metadata** - just pure Parquet files
- **Automated scripts** - easy to run and verify

Perfect for: MySQL/SQL Server → S3 Parquet data migration

---

## 🌩️ AWS EMR Production Deployment

### Prerequisites

1. **AWS EMR Cluster** running with Spark
2. **S3 Buckets** for code and data storage
3. **Database Access** from EMR (VPC/Security Groups configured)
4. **IAM Roles** with S3 and EMR permissions

### Step-by-Step AWS Deployment

#### Step 1: Configure Your Endpoints

Update the AWS endpoint files with your actual values:

**`src/main/resources/endpoints/mysql.aws.endpoint.yaml`:**
```yaml
endpoint_type: mysql
system: production
host: your-mysql-host.amazonaws.com  # ← Change this
port: 3306
schema_: your_database_name          # ← Change this
authentication:
  credential:
    username: your_mysql_username    # ← Change this
    password: your_mysql_password    # ← Change this
```

**`src/main/resources/endpoints/s3.aws.endpoint.yaml`:**
```yaml
endpoint_type: s3
bucket: your-data-bucket             # ← Change this
region: us-east-1                    # ← Change this if needed
authentication:
  credential:
    use_instance_profile: true       # EMR uses IAM roles
```

#### Step 2: Set Environment Variables

```bash
# Required: Your EMR cluster ID
export EMR_CLUSTER_ID="j-1234567890ABC"

# Required: S3 bucket for storing code
export S3_CODE_BUCKET="your-pipeline-code-bucket"

# Required: S3 bucket for output data
export S3_DATA_BUCKET="your-data-output-bucket"

# Optional: AWS region (default: us-east-1)
export AWS_REGION="us-east-1"
```

#### Step 3: Deploy and Run

```bash
# Deploy code and submit job to EMR
./scripts/deploy_to_emr.sh
```

#### Step 4: Monitor Progress

```bash
# Check job status
aws emr list-steps --cluster-id $EMR_CLUSTER_ID

# View logs (replace step-id with actual ID)
aws emr describe-step --cluster-id $EMR_CLUSTER_ID --step-id s-XXXXXXXXXX
```

#### Step 5: Check Results

Your Parquet files will be created in:
```
s3://your-data-bucket/
├── mysql_customers/
│   └── age_range=young/
│       └── part-*.snappy.parquet
└── mssql_customers/
    └── age_range=mature/
        └── part-*.snappy.parquet
```

### What the Deployment Does

1. **Packages** your code into a ZIP file
2. **Uploads** code to S3 code bucket
3. **Submits** Spark job to EMR cluster
4. **Runs** pipeline using `job.aws.parquet.yaml` configuration
5. **Saves** Parquet files to your data bucket

### Troubleshooting

**Common Issues:**

1. **"Cluster not found"** → Check EMR_CLUSTER_ID is correct
2. **"Access denied"** → Verify IAM roles have S3/EMR permissions
3. **"Database connection failed"** → Check VPC/Security Groups allow EMR → Database access
4. **"Bucket not found"** → Ensure S3 buckets exist and are accessible

**View EMR Logs:**
```bash
# SSH to EMR master node
aws emr ssh --cluster-id $EMR_CLUSTER_ID --key-pair-file your-key.pem

# Check Spark logs
sudo find /var/log/spark -name "*.log" | head -5
```

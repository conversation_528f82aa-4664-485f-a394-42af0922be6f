# Simple Database to Parquet Pipeline

Read data from MySQL/SQL Server databases and write to S3 as Parquet files.

## 🎯 What It Does

- **Reads**: MySQL and SQL Server customer data
- **Transforms**: Adds age_range column (young/mature)
- **Writes**: Parquet files to S3 with Snappy compression
- **Partitions**: By age_range for better performance

## 🚀 Quick Start

```bash
# 1. Download dependencies
./scripts/download_jars.sh

# 2. Start services
source scripts/setup_environment.sh && docker-compose up -d

# 3. Run pipeline
./scripts/run_pipeline.sh iceberg

# 4. Check results
./scripts/check_parquet_data.sh
python3 scripts/show_data_content.py
```

## 📁 Output Structure

Your data is saved as Parquet files in S3:

```
s3://mock-bucket/
├── mysql_customers/
│   └── age_range=young/
│       └── part-*.snappy.parquet    # MySQL customer data
└── mssql_customers/
    └── age_range=mature/
        └── part-*.snappy.parquet    # SQL Server customer data
```

**Partition Field**: `age_range` (young/mature based on age 20-40)

## ⚙️ Configuration

Simple job configuration in `src/main/resources/jobs/job.iceberg.sample.yaml`:

```yaml
jobs:
  - name: mysql_to_parquet
    readers:
      type: MysqlReader
      endpoint: mysql.endpoint.yaml
      table: customers
    transformers:
      - type: WithColumnTransformer
        columns:
          - colname: age_range
            expr: CASE WHEN age between 20 and 40 THEN 'young' ELSE 'mature' END
    writers:
      type: IcebergWriter
      endpoint: s3.localstack.endpoint.yaml
      s3_path: mysql_customers
      partition_columns:
        - age_range
```

## 🔧 Helper Scripts

All scripts are in the `scripts/` directory:

- `download_jars.sh` - Download required JAR files
- `setup_environment.sh` - Set environment variables
- `run_pipeline.sh iceberg` - Run the pipeline
- `check_parquet_data.sh` - Show Parquet files in S3
- `show_data_content.py` - Display actual data content

## 📊 Data Flow

1. **MySQL/SQL Server** → Read customer data
2. **Transform** → Add age_range column (young: 20-40, mature: >40)
3. **Partition** → Group by age_range
4. **Write** → Save as Parquet files to S3

## 🗂️ Project Structure

```
spark-metadata-driven/
├── scripts/                     # Helper scripts
├── src/main/
│   ├── writer/
│   │   └── iceberg_writer.py    # Simple Parquet writer
│   ├── resources/
│   │   ├── jobs/
│   │   │   └── job.iceberg.sample.yaml
│   │   └── jars/               # Downloaded JAR files
└── docker-compose.yml          # Local databases
```


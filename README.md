# Simple Database to Parquet Pipeline

Read data from MySQL/SQL Server databases and write to S3 as Parquet files.

## 🎯 What It Does

- **Reads**: MySQL and SQL Server customer data
- **Transforms**: Adds age_range column (young/mature)
- **Writes**: Parquet files to S3 with Snappy compression
- **Partitions**: By age_range for better performance

## 🚀 Quick Start

```bash
# 1. Download dependencies
./scripts/download_jars.sh

# 2. Start services
source scripts/setup_environment.sh && docker-compose up -d

# 3. Setup databases with sample data
./scripts/setup_databases.sh

# 4. Run pipeline (choose one)
./scripts/run_pipeline.sh iceberg

# OR run manually
python3 src/main/pipeline_main.py \
  --config_file job.iceberg.sample.yaml \
  --config_source_type local \
  --config_root_dir src/main/resources \
  --app_type batch \
  --s3_type localstack \
  --s3_url http://localhost:4566 \
  --extra_jars_folder src/main/resources/jars

# 5. Check results - view your data
python3 scripts/show_data.py
```

## 📁 Output Structure

Your data is saved as Parquet files in S3:

```
s3://mock-bucket/
├── mysql_customers/
│   └── age_range=young/
│       └── part-*.snappy.parquet    # MySQL customer data
└── mssql_customers/
    └── age_range=mature/
        └── part-*.snappy.parquet    # SQL Server customer data
```

**Partition Field**: `age_range` (young/mature based on age 20-40)

## ⚙️ Configuration

Simple job configuration in `src/main/resources/jobs/job.iceberg.sample.yaml`:

```yaml
jobs:
  - name: mysql_to_parquet
    readers:
      type: MysqlReader
      endpoint: mysql.endpoint.yaml
      table: customers
    transformers:
      - type: WithColumnTransformer
        columns:
          - colname: age_range
            expr: CASE WHEN age between 20 and 40 THEN 'young' ELSE 'mature' END
    writers:
      type: IcebergWriter
      endpoint: s3.localstack.endpoint.yaml
      s3_path: mysql_customers
      partition_columns:
        - age_range
```

## 🔧 Helper Scripts

All scripts are in the `scripts/` directory:

- `download_jars.sh` - Download required JAR files
- `setup_environment.sh` - Set environment variables
- `setup_databases.sh` - Create databases and sample data
- `run_pipeline.sh iceberg` - Run the pipeline
- `show_data.py` - View your data as clean dataframes

## 📊 Data Flow

1. **MySQL/SQL Server** → Read customer data
2. **Transform** → Add age_range column (young: 20-40, mature: >40)
3. **Partition** → Group by age_range
4. **Write** → Save as Parquet files to S3

## 🗂️ Project Structure

```
spark-metadata-driven/
├── scripts/                     # Helper scripts
├── src/main/
│   ├── writer/
│   │   └── iceberg_writer.py    # Simple Parquet writer
│   ├── resources/
│   │   ├── jobs/
│   │   │   └── job.iceberg.sample.yaml
│   │   └── jars/               # Downloaded JAR files
└── docker-compose.yml          # Local databases
```

## ✅ What You Get

- **Clean Parquet files** with Snappy compression
- **Partitioned data** for better query performance  
- **Simple S3 structure** - easy to understand
- **No complex metadata** - just pure Parquet files
- **Automated scripts** - easy to run and verify

Perfect for: MySQL/SQL Server → S3 Parquet data migration

## 📋 Sample Data

The `setup_databases.sh` script creates this sample data:

**MySQL & SQL Server customers table:**
```
+----+---------------+-----+---------------------+
| id | name          | age | updated_at          |
+----+---------------+-----+---------------------+
|  1 | John Doe      |  25 | 2025-01-03 12:00:00 |
|  2 | Jane Smith    |  35 | 2025-01-03 12:00:00 |
|  3 | Bob Johnson   |  45 | 2025-01-03 12:00:00 |
|  4 | Alice Brown   |  28 | 2025-01-03 12:00:00 |
|  5 | Charlie Wilson|  52 | 2025-01-03 12:00:00 |
+----+---------------+-----+---------------------+
```

**After pipeline transformation:**
- Ages 20-40 → `age_range = 'young'` (John, Jane, Alice)
- Ages >40 → `age_range = 'mature'` (Bob, Charlie)

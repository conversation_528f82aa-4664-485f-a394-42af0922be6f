#!/bin/bash

# Simple pipeline runner script
# Usage: ./run_pipeline.sh [standard|iceberg]

# Set up environment
source setup_environment.sh

# Default to standard pipeline
PIPELINE_TYPE=${1:-standard}

echo ""
echo "🚀 Running Spark Metadata-Driven Pipeline..."
echo "   Pipeline Type: $PIPELINE_TYPE"
echo ""

# Common parameters
COMMON_PARAMS="--config_source_type local --config_root_dir src/main/resources --app_type batch --s3_type localstack --s3_url http://localhost:4566 --extra_jars_folder src/main/resources/jars"

case $PIPELINE_TYPE in
    "standard")
        echo "📊 Running standard pipeline (PostgreSQL + S3)..."
        python3 src/main/pipeline_main.py --config_file job.sample.yaml $COMMON_PARAMS
        ;;
    "iceberg")
        echo "🏔️  Running Iceberg pipeline (Data Lake)..."
        python3 src/main/pipeline_main.py --config_file job.iceberg.sample.yaml $COMMON_PARAMS
        ;;
    *)
        echo "❌ Invalid pipeline type: $PIPELINE_TYPE"
        echo "   Usage: ./run_pipeline.sh [standard|iceberg]"
        echo "   Examples:"
        echo "     ./run_pipeline.sh standard  # Run PostgreSQL + S3 pipeline"
        echo "     ./run_pipeline.sh iceberg   # Run Iceberg data lake pipeline"
        exit 1
        ;;
esac

echo ""
echo "✅ Pipeline execution completed!"

if [ "$PIPELINE_TYPE" = "iceberg" ]; then
    echo ""
    echo "🔍 To check Iceberg data, run: ./check_iceberg_data.sh"
fi

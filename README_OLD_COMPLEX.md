# Spark Metadata-Driven ETL Pipeline

A configurable, metadata-driven ETL pipeline built with PySpark that supports multiple data sources, transformations, and destinations including databases, file storage, and data lakes. Designed for both local development and AWS EMR deployment.

## ✨ Features

- **Multiple Data Sources**: MySQL, SQL Server, PostgreSQL, S3
- **Flexible Transformations**: Projection, column addition, custom SQL
- **Multiple Output Formats**:
  - **Databases**: PostgreSQL
  - **File Storage**: S3 (Parquet, CSV, JSON, Avro, ORC, Excel, XML)
  - **Data Lakes**: Apache Iceberg with Parquet storage and ACID transactions
- **Configuration-Driven**: YAML-based job definitions
- **AWS Integration**: S3 storage with configurable authentication
- **Batch Processing**: Optimized for large-scale data processing
- **Helper Scripts**: Automated setup and management tools

## 🚀 Quick Start

**TL;DR - Get running in 3 commands:**
```bash
# 1. Download dependencies
./scripts/download_jars.sh

# 2. Setup environment and start services
source scripts/setup_environment.sh && docker-compose up -d

# 3. Run pipeline (choose one)
./scripts/run_pipeline.sh standard   # PostgreSQL + S3
./scripts/run_pipeline.sh iceberg    # Data Lake
```

## 📋 Detailed Setup

### Step 1: Install Prerequisites

#### **macOS - Install ODBC Drivers**
```bash
# Install unixODBC via Homebrew
brew install unixodbc

# Install Microsoft ODBC Driver for SQL Server
brew tap microsoft/mssql-release https://github.com/Microsoft/homebrew-mssql-release
brew install msodbcsql18 mssql-tools18

# Verify installation
odbcinst -q -d
# Should show: [ODBC Driver 18 for SQL Server]
```

#### **Download JAR Dependencies**
```bash
# Download ALL required JAR files (consolidated script)
./scripts/download_jars.sh
```

This consolidated script downloads ALL required JAR files to `src/main/resources/jars/`:
- **AWS SDK Bundle** for S3 integration
- **Hadoop AWS libraries** for S3 connectivity
- **Database drivers** (MySQL, PostgreSQL, SQL Server)
- **Apache Iceberg runtime** libraries for data lake operations

#### **Install Python Dependencies**
```bash
pip install -r requirements.txt
```

### Step 2: Set Environment Variables

```bash
# Use the automated setup script
source scripts/setup_environment.sh
```

This script automatically configures all required environment variables including:
- Configuration paths (CONFIG_ROOT_DIR, CONFIG_ENDPOINTS_DIR, CONFIG_JOBS_DIR)
- AWS credentials for LocalStack (AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, AWS_DEFAULT_REGION)
- S3 endpoint configuration (S3_ENDPOINT, S3_PATH_STYLE_ACCESS)
- JAR dependencies path (EXTRA_JARS_FOLDER)

### Step 3: Start Services

```bash
# Start Docker services (databases + LocalStack S3)
docker-compose up -d

# Verify services are running
docker-compose ps

# Check LocalStack S3 health
curl http://localhost:4566/health
```

### Step 4: Setup Databases and Sample Data

```bash
# Create databases and tables
docker exec -it mysql mysql -u test -p123456 -e "
CREATE DATABASE IF NOT EXISTS test;
USE test;
CREATE TABLE IF NOT EXISTS customers (
    id INT PRIMARY KEY,
    name VARCHAR(100),
    age INT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
INSERT INTO customers (id, name, age) VALUES
(1, 'John Doe', 25),
(2, 'Jane Smith', 35)
ON DUPLICATE KEY UPDATE name=VALUES(name);
"

# SQL Server setup
docker exec -it sqlserver /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P 'YourPassword123!' -C -Q "
IF NOT EXISTS (SELECT * FROM sys.databases WHERE name = 'test')
CREATE DATABASE test;
USE test;
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'customers')
CREATE TABLE customers (
    id INT PRIMARY KEY,
    name NVARCHAR(100),
    age INT,
    updated_at DATETIME DEFAULT GETDATE()
);
MERGE customers AS target
USING (VALUES (1, 'John Doe', 25), (2, 'Jane Smith', 35)) AS source (id, name, age)
ON target.id = source.id
WHEN MATCHED THEN UPDATE SET name = source.name, age = source.age
WHEN NOT MATCHED THEN INSERT (id, name, age) VALUES (source.id, source.name, source.age);
"

# PostgreSQL setup
docker exec -it postgres psql -U postgres -d test -c "
CREATE TABLE IF NOT EXISTS job_logger (
    id SERIAL PRIMARY KEY,
    system_name VARCHAR(100),
    schema_name VARCHAR(100),
    table_name VARCHAR(100),
    lower_bound TIMESTAMP,
    upper_bound TIMESTAMP,
    record_count INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS mysql_customers (
    id INT,
    name VARCHAR(100),
    age INT,
    range INT,
    _landing_loaded_at TIMESTAMP
);

CREATE TABLE IF NOT EXISTS mssql_customers (
    id INT,
    name VARCHAR(100),
    age INT,
    range INT,
    _landing_loaded_at TIMESTAMP
);

CREATE TABLE IF NOT EXISTS s3_customers (
    id INT,
    name VARCHAR(100),
    age INT,
    range INT,
    _landing_loaded_at TIMESTAMP
);
"

# Create S3 bucket in LocalStack
curl -X PUT http://localhost:4566/mock-bucket

# Upload sample JSON data to S3
echo '{"id":1,"name":"John Doe","age":25}
{"id":2,"name":"Jane Smith","age":35}
{"id":3,"name":"Bob Johnson","age":45}' > sample_data.json

curl -X PUT http://localhost:4566/mock-bucket/inbound/json_dummy/sample_data.json \
  --data-binary @sample_data.json

rm sample_data.json
```

### Step 5: Run the Pipeline

**Option A: Simple Script (Recommended)**
```bash
# Run standard pipeline (PostgreSQL + S3)
./scripts/run_pipeline.sh standard

# OR run Iceberg pipeline (Data Lake)
./scripts/run_pipeline.sh iceberg
```

**Option B: Manual Command**
```bash
# Standard pipeline
python3 src/main/pipeline_main.py \
  --config_file job.sample.yaml \
  --config_source_type local \
  --config_root_dir src/main/resources \
  --app_type batch \
  --s3_type localstack \
  --s3_url http://localhost:4566 \
  --extra_jars_folder src/main/resources/jars

# Iceberg pipeline
python3 src/main/pipeline_main.py \
  --config_file job.iceberg.sample.yaml \
  --config_source_type local \
  --config_root_dir src/main/resources \
  --app_type batch \
  --s3_type localstack \
  --s3_url http://localhost:4566 \
  --extra_jars_folder src/main/resources/jars
```

**Expected Output:**
```
INFO:sqlalchemy.engine.Engine:INSERT INTO test.public.job_logger ...
INFO:sqlalchemy.engine.Engine:COMMIT
Pipeline completed successfully!
```

## 📊 Check Output Data

### **PostgreSQL Tables**
```bash
# Check processed data in PostgreSQL
docker exec -it postgres psql -U postgres -d test -c "
SELECT 'mysql_customers' as table_name, COUNT(*) as count FROM mysql_customers
UNION ALL
SELECT 'mssql_customers' as table_name, COUNT(*) as count FROM mssql_customers
UNION ALL
SELECT 'job_logger' as table_name, COUNT(*) as count FROM job_logger;

-- View actual data
SELECT * FROM mysql_customers LIMIT 5;
SELECT * FROM mssql_customers LIMIT 5;
SELECT * FROM job_logger;
"
```

### **S3 Files (LocalStack)**
```bash
# List S3 files
curl -s http://localhost:4566/mock-bucket?list-type=2

# View S3 file content
curl -s http://localhost:4566/mock-bucket/outbound/json_dummy/part-00000-*.json
```

### **Expected Data Locations:**

1. **PostgreSQL Tables:**
   - `mysql_customers` - Data from MySQL with age_range calculation
   - `mssql_customers` - Data from SQL Server with age_range calculation
   - `job_logger` - Job execution metadata and bounds

2. **S3 Files:**
   - `s3://mock-bucket/outbound/json_dummy/` - Processed JSON files
   - Files include `_SUCCESS` marker and `part-*.json` data files

3. **Iceberg Tables (when using job.iceberg.sample.yaml):**
   - `iceberg_catalog.lakehouse.mysql_customers` - MySQL data in Iceberg format
   - `iceberg_catalog.lakehouse.mssql_customers` - SQL Server data in Iceberg format
   - `iceberg_catalog.lakehouse.s3_customers` - S3 data in Iceberg format
   - **Storage Location**: `s3://mock-bucket/iceberg-warehouse/lakehouse/`
   - **Format**: Parquet files with Snappy compression
   - **Features**: ACID transactions, schema evolution, time travel

### **Check Iceberg Data:**
```bash
# Use the provided script to check Iceberg files
./scripts/check_iceberg_data.sh
```

This will show you:
- Complete Iceberg warehouse structure
- All tables created in the lakehouse database
- Data and metadata file locations

## 🧪 Running Tests

```bash
# Run all tests
pytest -v

# Run specific test categories
pytest src/test/config/ -v      # Configuration tests
pytest src/test/reader/ -v      # Reader tests
pytest src/test/writer/ -v      # Writer tests
pytest src/test/transformer/ -v # Transformer tests
pytest src/test/utils/ -v       # Utility tests
```

## 🔍 Troubleshooting

### **Common Issues:**

1. **"YAML file is empty" error:**
   ```bash
   # Re-run environment setup
   source scripts/setup_environment.sh

   # Check environment variables
   echo $CONFIG_ROOT_DIR
   echo $CONFIG_ENDPOINTS_DIR

   # Verify file paths
   ls -la src/main/resources/endpoints/
   ```

2. **Database connection errors:**
   ```bash
   # Check Docker services
   docker-compose ps

   # Test database connections
   docker exec -it mysql mysql -u test -p123456 -e "SELECT 1"
   docker exec -it postgres psql -U postgres -d test -c "SELECT 1"
   ```

3. **S3/LocalStack errors:**
   ```bash
   # Check LocalStack health
   curl http://localhost:4566/health

   # List buckets
   curl http://localhost:4566/
   ```

4. **JAR file issues:**
   ```bash
   # Verify JAR files exist
   ls -la src/main/resources/jars/

   # Re-download if missing
   ./scripts/download_jars.sh
   ```

5. **Choosing Output Format:**
   - **PostgreSQL**: Use `job.sample.yaml` for relational database output
   - **S3 Files**: Use `job.sample.yaml` with S3Writer for file-based storage
   - **Iceberg Data Lake**: Use `job.iceberg.sample.yaml` for ACID transactions and schema evolution

   All formats can be run with the same command structure - just change the `--config_file` parameter.

### **Helper Scripts:**
```bash
# Setup environment (run once per session)
source scripts/setup_environment.sh

# Run pipeline (standard or iceberg)
./scripts/run_pipeline.sh [standard|iceberg]

# Check Iceberg data storage
./scripts/check_iceberg_data.sh

# Download JAR dependencies (if needed)
./scripts/download_jars.sh
```

### **Debug Commands:**
```bash
# Check environment variables
env | grep -E "(CONFIG_|AWS_|S3_)"

# Verify Docker services
docker-compose logs mysql
docker-compose logs postgres
docker-compose logs sqlserver
docker-compose logs localstack

# Test individual components
python3 -c "from config.environment import env_config; print(env_config.config_root_dir)"
```

## ☁️ AWS EMR Deployment

For production deployment on AWS EMR, see the [AWS EMR Guide](examples/aws-emr/README.md).

### **Quick EMR Setup:**
```bash
# Set EMR environment variables
export EMR_CLUSTER_ID="j-XXXXXXXXXXXXX"
export S3_CODE_BUCKET="your-company-spark-code"
export S3_CONFIG_BUCKET="your-company-spark-configs"
export S3_DATA_BUCKET="your-company-data-lake"

# Deploy to EMR
cd examples/aws-emr
./deploy-to-emr.sh
```

### **Parquet Output on S3:**
When using parquet format, files are saved with partitioning:
```
s3://your-data-bucket/processed-data/customers/
├── year=2025/
│   └── month=01/
│       └── day=15/
│           ├── age_range=1/
│           │   └── part-00000-abc123.parquet
│           ├── age_range=2/
│           │   └── part-00000-def456.parquet
│           └── _SUCCESS
```

### **Iceberg Output on S3:**
When using Iceberg writer, data is stored in a managed warehouse structure:
```
s3://your-data-bucket/iceberg-warehouse/
├── lakehouse/                    # Database
│   ├── customers/               # Table
│   │   ├── metadata/           # Iceberg metadata files
│   │   │   ├── version-hint.text
│   │   │   ├── v1.metadata.json
│   │   │   └── snap-*.avro
│   │   └── data/               # Parquet data files
│   │       ├── age_range=young/
│   │       │   └── 00000-0-*.parquet
│   │       └── age_range=mature/
│   │           └── 00000-1-*.parquet
│   └── other_tables/
└── other_databases/
```

**Iceberg Benefits:**
- **ACID Transactions**: Atomic writes with rollback capability
- **Schema Evolution**: Add/modify columns without breaking existing queries
- **Time Travel**: Query historical versions of data
- **Efficient Updates**: Row-level updates and deletes

## 🏗️ Project Structure

```
spark-metadata-driven/
├── scripts/                 # Helper scripts directory
│   ├── setup_environment.sh    # Environment setup script
│   ├── run_pipeline.sh         # Simple pipeline runner
│   ├── check_iceberg_data.sh   # Iceberg data verification script
│   └── download_jars.sh        # Consolidated JAR dependencies download
├── src/main/
│   ├── config/           # Configuration models
│   │   ├── environment.py    # Environment-based config
│   │   ├── endpoint.py       # Database/S3 endpoints
│   │   └── job.py           # Job configurations
│   ├── reader/           # Data readers
│   │   ├── mysql_reader.py
│   │   ├── mssql_reader.py
│   │   └── s3_reader.py
│   ├── writer/           # Data writers
│   │   ├── postgre_writer.py
│   │   ├── s3_writer.py
│   │   └── iceberg_writer.py
│   ├── transformer/      # Data transformations
│   ├── utils/           # Utilities
│   │   ├── secrets_manager.py # AWS Secrets Manager
│   │   └── file_util.py      # S3/Local file handling
│   └── resources/       # Configuration files
│       ├── endpoints/   # Database/S3 endpoint configs
│       ├── jobs/        # Job definition files
│       │   ├── job.sample.yaml        # Standard pipeline jobs
│       │   └── job.iceberg.sample.yaml # Iceberg pipeline jobs
│       └── jars/        # JAR dependencies
├── examples/aws-emr/    # AWS EMR deployment
└── docker-compose.yml   # Local development services
```

## 🔧 Configuration

### **Job Configuration (YAML):**
```yaml
jobs:
  - job_id: "customer_pipeline"
    reader:
      type: "MysqlReader"
      endpoint: "mysql.endpoint.yaml"
      table: "customers"
      strategy: "delta"
      delta_column: "updated_at"
    transformers:
      - type: "WithColumnTransformer"
        column_name: "age_range"
        expression: "CASE WHEN age < 30 THEN 1 ELSE 2 END"
    writer:
      type: "S3Writer"
      endpoint: "s3.endpoint.yaml"
      key: "processed-data/customers/"
      format:
        type: "ParquetWriterFormatConfig"
        compression: "snappy"
```

### **Iceberg Configuration (YAML):**
```yaml
jobs:
  - job_id: "iceberg_pipeline"
    reader:
      type: "MysqlReader"
      endpoint: "mysql.endpoint.yaml"
      table: "customers"
      strategy: "delta"
      delta_column: "updated_at"
    transformers:
      - type: "WithColumnTransformer"
        column_name: "age_range"
        expression: "CASE WHEN age < 30 THEN 'young' ELSE 'mature' END"
    writer:
      type: "IcebergWriter"
      endpoint: "iceberg.s3.endpoint.yaml"
      catalog_name: "iceberg_catalog"
      database: "lakehouse"
      table: "customers"
      mode: "append"
      partition_columns:
        - "age_range"
      table_properties:
        write.format.default: "parquet"
        write.parquet.compression-codec: "snappy"
      format:
        type: "IcebergWriterFormat"
        source_options:
          write.format.default: "parquet"
```

### **Endpoint Configuration (YAML):**
```yaml
# Database endpoint
endpoint_type: mysql
system: production
host: ${DB_HOST}
port: 3306
schema_: ${DB_SCHEMA}
authentication:
  credential:
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}

# S3 endpoint
endpoint_type: s3
bucket: ${S3_DATA_BUCKET}
authentication:
  credential:
    access_key_id: ${AWS_ACCESS_KEY_ID}
    secret_access_key: ${AWS_SECRET_ACCESS_KEY}
```

## 📈 Features

- ✅ **Metadata-driven**: Configure pipelines via YAML files
- ✅ **Multi-source**: MySQL, SQL Server, PostgreSQL, S3 support
- ✅ **Delta loading**: Incremental data processing with bounds tracking
- ✅ **Transformations**: Column calculations, projections, filtering
- ✅ **Multiple formats**: JSON, Parquet, CSV, Avro support
- ✅ **AWS integration**: EMR, S3, Secrets Manager, IAM roles
- ✅ **Environment-based**: No hardcoded values, configurable for any environment
- ✅ **Monitoring**: Job execution tracking and logging

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite: `pytest -v`
6. Submit a pull request

## � Summary

This Spark Metadata-Driven ETL Pipeline provides:

### **🎯 Output Options:**
- **Standard Pipeline**: `./scripts/run_pipeline.sh standard`
  - Writes to PostgreSQL databases
  - Saves files to S3 storage
- **Iceberg Pipeline**: `./scripts/run_pipeline.sh iceberg`
  - Creates data lake tables with ACID transactions
  - Supports schema evolution and time travel
  - Stores data in Parquet format with Snappy compression

### **📁 Data Storage Locations:**
- **PostgreSQL**: Tables in `test` database
- **S3 Files**: `s3://mock-bucket/outbound/`
- **Iceberg Tables**: `s3://mock-bucket/iceberg-warehouse/lakehouse/`

### **🛠️ Helper Scripts:**
All scripts are organized in the `scripts/` directory:
- `download_jars.sh` - Downloads ALL dependencies (AWS, databases, Iceberg)
- `setup_environment.sh` - Sets environment variables
- `run_pipeline.sh` - Runs standard or Iceberg pipeline
- `check_iceberg_data.sh` - Verifies Iceberg data storage

### **🔧 Key Improvements:**
- ✅ **Consolidated JAR downloads** - Single script for all dependencies
- ✅ **Organized scripts** - All helper scripts in `scripts/` folder
- ✅ **No environment duplication** - Single source of truth
- ✅ **Iceberg as standard option** - Equal to PostgreSQL/S3 outputs
- ✅ **Simple commands** - Easy-to-use wrapper scripts

## �📄 License

This project is licensed under the MIT License.

"""
Simple Iceberg Writer for Spark Metadata-Driven Pipeline
Writes data directly to S3 as Parquet files with simple folder structure
No complex catalog/table management - just organized Parquet files
"""

from writer.writer import Writer
from config.writer import IcebergWriterConfig
from pyspark.sql.dataframe import DataFrame
from pyspark.sql.functions import current_timestamp
from typing import Optional
import logging


class IcebergWriter(Writer):
    """
    Simple writer that outputs Parquet files to S3 in organized structure
    Purpose: Read from MySQL/MSSQL and write to S3 as Parquet files
    """

    def __init__(self, config: IcebergWriterConfig, process_date: str = None, custom_options: Optional[dict[str, str]] = None):
        self.config = config
        self.process_date = process_date
        self.custom_options = custom_options or {}
        self.endpoint = config.endpoint
        self.logger = logging.getLogger(self.__class__.__name__)

    def write(self, df: DataFrame):
        """
        Write DataFrame as Parquet files to S3

        Args:
            df: Spark DataFrame to write
        """
        try:
            record_count = df.count()
            self.logger.info(
                f"Writing {record_count} records as Parquet files to S3")

            # Add metadata column
            final_df = df.withColumn("_loaded_at", current_timestamp())

            # Build S3 path: s3://bucket/path/
            endpoint_config = self.config.endpoint_config
            bucket = endpoint_config.bucket  # type: ignore
            s3_path = f"s3a://{bucket}/{self.config.s3_path}/"

            self.logger.info(f"Writing Parquet files to: {s3_path}")

            # Configure writer for Parquet output
            writer = final_df.write.mode(self.config.mode).format("parquet")

            # Add partitioning if specified
            if self.config.partition_columns:
                writer = writer.partitionBy(*self.config.partition_columns)
                self.logger.info(
                    f"Partitioning by: {self.config.partition_columns}")

            # Set Parquet compression (default: snappy)
            compression = "snappy"
            if self.config.format and self.config.format.source_options:
                compression = self.config.format.source_options.get(
                    "compression", "snappy")

            writer = writer.option("compression", compression)

            # Apply any custom options
            if self.custom_options:
                for key, value in self.custom_options.items():
                    writer = writer.option(key, value)

            # Write Parquet files to S3
            writer.save(s3_path)

            self.logger.info(
                f"Successfully wrote {record_count} records as Parquet files to {s3_path}")

        except Exception as e:
            self.logger.error(f"Failed to write Parquet files: {e}")
            raise

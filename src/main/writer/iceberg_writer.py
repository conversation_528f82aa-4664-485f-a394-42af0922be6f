"""
Apache Iceberg Writer for Spark Metadata-Driven Pipeline
Supports writing data to Iceberg tables with Parquet format
"""

from writer.writer import Writer
from config.writer import IcebergWriterConfig
from pyspark.sql.dataframe import DataFrame
from pyspark.sql.functions import current_timestamp
from pyspark.sql import SparkSession
from typing import Optional
import logging


class IcebergWriter(Writer):
    """
    Writer implementation for Apache Iceberg tables
    Supports ACID transactions, schema evolution, and time travel
    """

    def __init__(self, config: IcebergWriterConfig, process_date: str = None, custom_options: Optional[dict[str, str]] = None):
        self.config = config
        self.process_date = process_date
        self.custom_options = custom_options or {}
        self.endpoint = config.endpoint
        self.logger = logging.getLogger(self.__class__.__name__)

    def write(self, df: DataFrame):
        """
        Write DataFrame to Iceberg table

        Args:
            df: Spark DataFrame to write
        """
        try:
            # Get Spark session
            spark = df.sparkSession

            # Note: Iceberg catalog should be configured at Spark session creation time

            # Build table identifier using dedicated iceberg catalog
            table_identifier = f"iceberg_catalog.{self.config.database}.{self.config.table}"

            # Add landing timestamp
            final_df = df.withColumn("_landing_loaded_at", current_timestamp())

            # Create table if it doesn't exist
            self._create_table_if_not_exists(spark, final_df, table_identifier)

            # Configure write options
            write_options = self._get_write_options()

            # Write data to Iceberg table
            writer = final_df.write.format("iceberg").mode(self.config.mode)

            # Apply custom options if provided
            if self.custom_options:
                writer = writer.options(**self.custom_options)

            # Apply write options
            if write_options:
                writer = writer.options(**write_options)

            # Partition if specified
            if self.config.partition_columns:
                writer = writer.partitionBy(*self.config.partition_columns)

            # Write to table
            writer.saveAsTable(table_identifier)

            self.logger.info(
                f"Successfully wrote {final_df.count()} records to Iceberg table: {table_identifier}")

        except Exception as e:
            self.logger.error(f"Failed to write to Iceberg table: {str(e)}")
            raise

    def _create_table_if_not_exists(self, spark: SparkSession, df: DataFrame, table_identifier: str):
        """Create Iceberg table if it doesn't exist"""
        try:
            # Check if table exists
            spark.sql(f"DESCRIBE TABLE {table_identifier}")
            self.logger.info(f"Table {table_identifier} already exists")
        except Exception:
            # Table doesn't exist, create it
            self.logger.info(f"Creating Iceberg table: {table_identifier}")

            # Create database if it doesn't exist
            spark.sql(
                f"CREATE DATABASE IF NOT EXISTS iceberg_catalog.{self.config.database}")

            # Build CREATE TABLE statement
            create_table_sql = self._build_create_table_sql(
                df, table_identifier)

            # Execute CREATE TABLE
            spark.sql(create_table_sql)
            self.logger.info(
                f"Successfully created Iceberg table: {table_identifier}")

    def _build_create_table_sql(self, df: DataFrame, table_identifier: str) -> str:
        """Build CREATE TABLE SQL statement for Iceberg"""
        # Get schema from DataFrame
        schema_fields = []
        for field in df.schema.fields:
            field_type = field.dataType.simpleString()
            # Iceberg doesn't support NULL/NOT NULL in column definitions
            schema_fields.append(f"{field.name} {field_type}")

        schema_str = ",\n    ".join(schema_fields)

        # Build CREATE TABLE statement
        create_sql = f"""
        CREATE TABLE {table_identifier} (
            {schema_str}
        ) USING ICEBERG
        """

        # Add partitioning if specified
        if self.config.partition_columns:
            partition_str = ", ".join(self.config.partition_columns)
            create_sql += f"\nPARTITIONED BY ({partition_str})"

        # Add table properties if specified
        if self.config.table_properties:
            properties = []
            for key, value in self.config.table_properties.items():
                properties.append(f"'{key}' = '{value}'")
            properties_str = ", ".join(properties)
            create_sql += f"\nTBLPROPERTIES ({properties_str})"

        return create_sql

    def _get_write_options(self) -> dict[str, str]:
        """Get write-specific options"""
        options = {}

        # Add format-specific options
        if self.config.format.source_options:
            options.update(self.config.format.source_options)

        # Default Iceberg write options for Parquet
        default_options = {
            "write.format.default": "parquet",
            "write.parquet.compression-codec": "snappy"
        }

        # Merge with defaults (custom options take precedence)
        for key, value in default_options.items():
            if key not in options:
                options[key] = value

        return options

jobs:
  # Job 1: MySQL to Iceberg Table
  - name: mysql_to_iceberg
    readers:
      type: MysqlReader
      endpoint: mysql.endpoint.yaml
      table: customers
      strategy: delta
      delta_column: updated_at
    transformers:
       - type: WithColumnTransformer
         columns:
           - colname: age_range
             expr: CASE WHEN age between 20 and 40 THEN 'young' ELSE 'mature' END
       - type: ProjectionTransformer
         columns: 
           - id
           - name  
           - age
           - age_range
    writers:
      type: IcebergWriter
      endpoint: iceberg.s3.endpoint.yaml
      catalog_name: iceberg_catalog
      database: lakehouse
      table: mysql_customers
      mode: append
      partition_columns:
        - age_range
      table_properties:
        write.format.default: parquet
        write.parquet.compression-codec: snappy
        write.target-file-size-bytes: "134217728"  # 128MB
      format:
        type: IcebergWriterFormat
        source_options:
          write.format.default: parquet
          write.parquet.compression-codec: snappy

  # Job 2: SQL Server to Iceberg Table  
  - name: mssql_to_iceberg
    readers:
      type: MssqlReader
      endpoint: mssql.endpoint.yaml
      table: customers
      strategy: delta
      delta_column: updated_at
    transformers:
       - type: WithColumnTransformer
         columns:
           - colname: age_range
             expr: CASE WHEN age between 20 and 40 THEN 'young' ELSE 'mature' END
       - type: ProjectionTransformer
         columns: 
           - id
           - name  
           - age
           - age_range
    writers:
      type: IcebergWriter
      endpoint: iceberg.s3.endpoint.yaml
      catalog_name: iceberg_catalog
      database: lakehouse
      table: mssql_customers
      mode: append
      partition_columns:
        - age_range
      table_properties:
        write.format.default: parquet
        write.parquet.compression-codec: snappy
        write.target-file-size-bytes: "134217728"  # 128MB
      format:
        type: IcebergWriterFormat
        source_options:
          write.format.default: parquet
          write.parquet.compression-codec: snappy

jobs:
  # Job 1: MySQL to Parquet Files (Simple Iceberg Structure)
  - name: mysql_to_parquet
    readers:
      type: MysqlReader
      endpoint: mysql.endpoint.yaml
      table: customers
      strategy: delta
      delta_column: updated_at
    transformers:
       - type: WithColumnTransformer
         columns:
           - colname: age_range
             expr: CASE WHEN age between 20 and 40 THEN 'young' ELSE 'mature' END
       - type: ProjectionTransformer
         columns:
           - id
           - name
           - age
           - age_range
    writers:
      type: IcebergWriter
      endpoint: s3.localstack.endpoint.yaml
      database: mysql_data
      table: customers
      mode: append
      partition_columns:
        - age_range
      format:
        type: IcebergWriterFormat
        source_options:
          compression: snappy

  # Job 2: SQL Server to Parquet Files (Simple Iceberg Structure)
  - name: mssql_to_parquet
    readers:
      type: MssqlReader
      endpoint: mssql.endpoint.yaml
      table: customers
      strategy: delta
      delta_column: updated_at
    transformers:
       - type: WithColumnTransformer
         columns:
           - colname: age_range
             expr: CASE WHEN age between 20 and 40 THEN 'young' ELSE 'mature' END
       - type: ProjectionTransformer
         columns:
           - id
           - name
           - age
           - age_range
    writers:
      type: IcebergWriter
      endpoint: s3.localstack.endpoint.yaml
      database: mssql_data
      table: customers
      mode: append
      partition_columns:
        - age_range
      format:
        type: IcebergWriterFormat
        source_options:
          compression: snappy

  # Job 3: S3 JSON to Parquet Files (Simple Iceberg Structure)
  - name: s3_to_parquet
    readers:
      type: S3Reader
      endpoint: s3.localstack.endpoint.yaml
      key: inbound/json_dummy
      format:
        type: JsonReaderFormat
        source_options:
          multiline: "true"
    transformers:
       - type: WithColumnTransformer
         columns:
           - colname: age_range
             expr: CASE WHEN age between 20 and 40 THEN 'young' ELSE 'mature' END
           - colname: data_source
             expr: "'s3_json'"
       - type: ProjectionTransformer
         columns:
           - id
           - name
           - age
           - age_range
           - data_source
    writers:
      type: IcebergWriter
      endpoint: s3.localstack.endpoint.yaml
      database: s3_data
      table: customers
      mode: append
      partition_columns:
        - data_source
        - age_range
      format:
        type: IcebergWriterFormat
        source_options:
          compression: snappy

#!/bin/bash

# AWS EMR Deployment Script for Spark Metadata-Driven Pipeline
# This script uploads the pipeline code and configurations to S3 and submits the job to EMR

set -e

# Configuration
EMR_CLUSTER_ID="${EMR_CLUSTER_ID}"
S3_CODE_BUCKET="${S3_CODE_BUCKET}"
S3_CONFIG_BUCKET="${S3_CONFIG_BUCKET}"
AWS_REGION="${AWS_REGION:-us-east-1}"
JOB_CONFIG_FILE="${JOB_CONFIG_FILE:-production-etl.yaml}"

# Validate required environment variables
if [ -z "$EMR_CLUSTER_ID" ]; then
    echo "Error: EMR_CLUSTER_ID environment variable is required"
    exit 1
fi

if [ -z "$S3_CODE_BUCKET" ]; then
    echo "Error: S3_CODE_BUCKET environment variable is required"
    exit 1
fi

if [ -z "$S3_CONFIG_BUCKET" ]; then
    echo "Error: S3_CONFIG_BUCKET environment variable is required"
    exit 1
fi

echo "Deploying Spark Metadata-Driven Pipeline to EMR..."
echo "EMR Cluster ID: $EMR_CLUSTER_ID"
echo "S3 Code Bucket: $S3_CODE_BUCKET"
echo "S3 Config Bucket: $S3_CONFIG_BUCKET"
echo "Job Config: $JOB_CONFIG_FILE"

# Create deployment timestamp
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
DEPLOYMENT_PREFIX="deployments/$TIMESTAMP"

# 1. Upload pipeline code to S3
echo "Uploading pipeline code to S3..."
aws s3 sync ../../src/ s3://$S3_CODE_BUCKET/$DEPLOYMENT_PREFIX/src/ \
    --exclude "*.pyc" \
    --exclude "__pycache__/*" \
    --exclude "*.log"

# 2. Upload configuration files to S3
echo "Uploading configuration files to S3..."
aws s3 sync ./endpoints/ s3://$S3_CONFIG_BUCKET/configs/endpoints/
aws s3 sync ./jobs/ s3://$S3_CONFIG_BUCKET/configs/jobs/

# 3. Create EMR step configuration
STEP_NAME="spark-metadata-pipeline-$TIMESTAMP"
MAIN_PY_PATH="s3://$S3_CODE_BUCKET/$DEPLOYMENT_PREFIX/src/main/pipeline_main.py"

# Environment variables for the job
ENV_VARS=(
    "CONFIG_ROOT_DIR=s3://$S3_CONFIG_BUCKET/configs"
    "AWS_DEFAULT_REGION=$AWS_REGION"
    "EMR_CLUSTER_ID=$EMR_CLUSTER_ID"
    "DB_SECRETS_MANAGER_ENABLED=${DB_SECRETS_MANAGER_ENABLED:-false}"
    "APP_NAME_PREFIX=${APP_NAME_PREFIX:-spark-metadata-pipeline}"
    "LOG_LEVEL=${LOG_LEVEL:-INFO}"
)

# Job-specific environment variables (customize as needed)
ENV_VARS+=(
    "SOURCE_TABLE_NAME=${SOURCE_TABLE_NAME:-customers}"
    "DELTA_COLUMN=${DELTA_COLUMN:-updated_at}"
    "OUTPUT_S3_PREFIX=${OUTPUT_S3_PREFIX:-processed-data}"
    "INPUT_S3_PREFIX=${INPUT_S3_PREFIX:-processed-data}"
    "TARGET_TABLE_NAME=${TARGET_TABLE_NAME:-dim_customers}"
    "WRITE_MODE=${WRITE_MODE:-append}"
    "WAREHOUSE_WRITE_MODE=${WAREHOUSE_WRITE_MODE:-overwrite}"
    "NUM_PARTITIONS=${NUM_PARTITIONS:-4}"
    "OUTPUT_NUM_FILES=${OUTPUT_NUM_FILES:-1}"
    "PARQUET_COMPRESSION=${PARQUET_COMPRESSION:-snappy}"
    "YEAR=$(date +%Y)"
    "MONTH=$(date +%m)"
    "DAY=$(date +%d)"
)

# Build spark-submit command
SPARK_SUBMIT_ARGS=(
    "--deploy-mode" "cluster"
    "--conf" "spark.pyspark.driver.python=python3"
    "--conf" "spark.pyspark.python=python3"
    "--py-files" "s3://$S3_CODE_BUCKET/$DEPLOYMENT_PREFIX/src.zip"
)

# Add environment variables as Spark configurations
for env_var in "${ENV_VARS[@]}"; do
    SPARK_SUBMIT_ARGS+=("--conf" "spark.executorEnv.${env_var}")
    SPARK_SUBMIT_ARGS+=("--conf" "spark.yarn.appMasterEnv.${env_var}")
done

# Application arguments
APP_ARGS=(
    "--config_file" "$JOB_CONFIG_FILE"
    "--config_source_type" "s3"
    "--config_root_dir" "s3://$S3_CONFIG_BUCKET/configs"
    "--app_type" "batch"
    "--s3_type" "aws"
)

# 4. Create Python package for deployment
echo "Creating Python package..."
cd ../../src
zip -r ../examples/aws-emr/src.zip . -x "*.pyc" "*/__pycache__/*" "*.log"
cd ../examples/aws-emr

# Upload Python package
aws s3 cp src.zip s3://$S3_CODE_BUCKET/$DEPLOYMENT_PREFIX/

# 5. Submit EMR step
echo "Submitting EMR step..."
STEP_ID=$(aws emr add-steps \
    --cluster-id $EMR_CLUSTER_ID \
    --steps Type=Spark,Name="$STEP_NAME",ActionOnFailure=CONTINUE,Args=["${SPARK_SUBMIT_ARGS[@]}","$MAIN_PY_PATH","${APP_ARGS[@]}"] \
    --query 'StepIds[0]' \
    --output text)

echo "EMR Step submitted successfully!"
echo "Step ID: $STEP_ID"
echo "Monitor progress with: aws emr describe-step --cluster-id $EMR_CLUSTER_ID --step-id $STEP_ID"

# 6. Optional: Wait for step completion
if [ "${WAIT_FOR_COMPLETION:-false}" = "true" ]; then
    echo "Waiting for step completion..."
    aws emr wait step-complete --cluster-id $EMR_CLUSTER_ID --step-id $STEP_ID
    
    # Get step status
    STEP_STATUS=$(aws emr describe-step --cluster-id $EMR_CLUSTER_ID --step-id $STEP_ID --query 'Step.Status.State' --output text)
    echo "Step completed with status: $STEP_STATUS"
    
    if [ "$STEP_STATUS" = "COMPLETED" ]; then
        echo "Pipeline executed successfully!"
        exit 0
    else
        echo "Pipeline failed. Check EMR logs for details."
        exit 1
    fi
fi

echo "Deployment completed. Check EMR console for job status."

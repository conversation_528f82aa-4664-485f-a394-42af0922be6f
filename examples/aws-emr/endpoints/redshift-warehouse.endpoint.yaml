# AWS Redshift Data Warehouse endpoint configuration
endpoint_type: postgresql  # Redshift uses PostgreSQL protocol
system: warehouse
host: ${REDSHIFT_HOST}  # e.g., your-cluster.abc123.us-east-1.redshift.amazonaws.com
port: 5439
database: ${REDSHIFT_DATABASE}
schema_: ${REDSHIFT_SCHEMA:public}
authentication:
  credential:
    username: ${REDSHIFT_USERNAME}
    password: ${REDSHIFT_PASSWORD}

# Optional: AWS Secrets Manager configuration
secrets_manager:
  enabled: ${REDSHIFT_SECRETS_MANAGER_ENABLED:false}
  secret_name: ${REDSHIFT_SECRET_NAME}
  region: ${AWS_DEFAULT_REGION:us-east-1}

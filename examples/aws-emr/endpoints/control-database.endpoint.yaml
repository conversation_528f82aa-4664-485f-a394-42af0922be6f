# Control database for job tracking (can be RDS PostgreSQL or Redshift)
endpoint_type: postgresql
system: control
host: ${CONTROL_DB_HOST}
port: ${CONTROL_DB_PORT:5432}
database: ${CONTROL_DB_DATABASE}
schema_: ${CONTROL_DB_SCHEMA:public}
control_table: ${CONTROL_TABLE_NAME:job_logger}
system_name_column: system_name
schema_name_column: schema_name
table_name_column: table_name
authentication:
  credential:
    username: ${CONTROL_DB_USERNAME}
    password: ${CONTROL_DB_PASSWORD}

# Optional: AWS Secrets Manager configuration
secrets_manager:
  enabled: ${CONTROL_DB_SECRETS_MANAGER_ENABLED:false}
  secret_name: ${CONTROL_DB_SECRET_NAME}
  region: ${AWS_DEFAULT_REGION:us-east-1}

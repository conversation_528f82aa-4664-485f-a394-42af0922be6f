# AWS S3 Data Lake endpoint configuration
# Uses IAM roles on EMR for authentication
endpoint_type: s3
bucket: ${S3_DATA_BUCKET}  # e.g., your-company-data-lake

# Authentication will use IAM roles on EMR
# No need to specify credentials when running on EMR
authentication:
  # Leave empty - EMR will use IAM instance profile
  credential:
    access_key_id: ""
    secret_access_key: ""
    session_token: ""

# Optional: For cross-account access or specific credentials
# authentication:
#   credential:
#     access_key_id: ${AWS_ACCESS_KEY_ID}
#     secret_access_key: ${AWS_SECRET_ACCESS_KEY}
#     session_token: ${AWS_SESSION_TOKEN}

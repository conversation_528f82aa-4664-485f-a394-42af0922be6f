# AWS RDS MySQL endpoint configuration
# Use AWS Secrets Manager for credentials
endpoint_type: mysql
system: production
host: ${DB_HOST}  # Environment variable or use actual RDS endpoint
port: 3306
schema_: ${DB_SCHEMA}
authentication:
  credential:
    username: ${DB_USERNAME}  # Will be overridden by secrets manager if enabled
    password: ${DB_PASSWORD}  # Will be overridden by secrets manager if enabled

# Optional: AWS Secrets Manager configuration
# Set DB_SECRETS_MANAGER_ENABLED=true and DB_SECRET_NAME environment variables
# The secret should contain: {"username": "user", "password": "pass", "host": "host", "database": "db"}
secrets_manager:
  enabled: ${DB_SECRETS_MANAGER_ENABLED:false}
  secret_name: ${DB_SECRET_NAME}
  region: ${AWS_DEFAULT_REGION:us-east-1}

# Production ETL job configuration for AWS EMR
# All paths and configurations use environment variables

jobs:
  - job_id: "customer_data_pipeline"
    reader:
      type: "MysqlReader"
      endpoint: "rds-mysql.endpoint.yaml"
      table: "${SOURCE_TABLE_NAME:customers}"
      strategy: "delta"
      delta_column: "${DELTA_COLUMN:updated_at}"
      num_partitions: ${NUM_PARTITIONS:4}
    
    transformers:
      - type: "WithColumnTransformer"
        column_name: "age_range"
        expression: "CASE WHEN age < 30 THEN 1 WHEN age < 50 THEN 2 ELSE 3 END"
      - type: "ProjectionTransformer"
        columns: ["id", "name", "age", "age_range"]
    
    writer:
      type: "S3Writer"
      endpoint: "s3-data-lake.endpoint.yaml"
      key: "${OUTPUT_S3_PREFIX}/customers/year=${YEAR}/month=${MONTH}/day=${DAY}/"
      mode: "${WRITE_MODE:append}"
      num_files: ${OUTPUT_NUM_FILES:1}
      partition_columns: ["age_range"]
      format:
        type: "ParquetWriterFormatConfig"
        compression: "${PARQUET_COMPRESSION:snappy}"

  - job_id: "customer_warehouse_load"
    reader:
      type: "S3Reader"
      endpoint: "s3-data-lake.endpoint.yaml"
      key: "${INPUT_S3_PREFIX}/customers/year=${YEAR}/month=${MONTH}/day=${DAY}/"
      format:
        type: "ParquetReaderFormatConfig"
      num_partitions: ${NUM_PARTITIONS:4}
    
    transformers:
      - type: "WithColumnTransformer"
        column_name: "load_timestamp"
        expression: "current_timestamp()"
    
    writer:
      type: "PostgresWriter"  # For Redshift
      endpoint: "redshift-warehouse.endpoint.yaml"
      table: "${TARGET_TABLE_NAME:dim_customers}"
      mode: "${WAREHOUSE_WRITE_MODE:overwrite}"
      ssl_mode: "require"

# AWS EMR Deployment Guide

This guide explains how to deploy the Spark Metadata-Driven Pipeline to AWS EMR without hardcoded values.

## Overview

The refactored pipeline supports:
- ✅ **Environment-based configuration** - No hardcoded values
- ✅ **AWS Secrets Manager integration** - Secure credential management
- ✅ **S3-based configuration storage** - Centralized config management
- ✅ **EMR-optimized Spark settings** - Automatic EMR detection
- ✅ **Flexible output formats** - Including Parquet support
- ✅ **IAM role-based authentication** - No embedded credentials

## Prerequisites

1. **AWS EMR Cluster** - Running with Spark support
2. **S3 Buckets** - For code, configurations, and data
3. **IAM Roles** - Proper permissions for EMR, S3, RDS, Secrets Manager
4. **Database Endpoints** - RDS, Redshift, or other JDBC sources

## Environment Variables

### Required Variables
```bash
# EMR Configuration
export EMR_CLUSTER_ID="j-XXXXXXXXXXXXX"
export S3_CODE_BUCKET="your-company-spark-code"
export S3_CONFIG_BUCKET="your-company-spark-configs"
export AWS_REGION="us-east-1"

# Database Configuration
export DB_HOST="your-rds-endpoint.amazonaws.com"
export DB_USERNAME="your_username"
export DB_PASSWORD="your_password"
export DB_SCHEMA="your_schema"

# Control Database
export CONTROL_DB_HOST="your-control-db.amazonaws.com"
export CONTROL_DB_USERNAME="control_user"
export CONTROL_DB_PASSWORD="control_password"
export CONTROL_DB_DATABASE="control_db"

# S3 Data Lake
export S3_DATA_BUCKET="your-company-data-lake"
```

### Optional Variables
```bash
# Secrets Manager (Recommended for Production)
export DB_SECRETS_MANAGER_ENABLED="true"
export DB_SECRET_NAME="prod/database/credentials"
export CONTROL_DB_SECRETS_MANAGER_ENABLED="true"
export CONTROL_DB_SECRET_NAME="prod/control-db/credentials"

# Job Configuration
export SOURCE_TABLE_NAME="customers"
export TARGET_TABLE_NAME="dim_customers"
export OUTPUT_S3_PREFIX="processed-data"
export NUM_PARTITIONS="8"
export PARQUET_COMPRESSION="snappy"

# Application Settings
export APP_NAME_PREFIX="your-company-etl"
export LOG_LEVEL="INFO"
```

## Deployment Steps

### 1. Prepare Configuration Files

Update the endpoint configurations with your actual values:

```bash
# Copy example configurations
cp -r examples/aws-emr/endpoints/ your-configs/endpoints/
cp -r examples/aws-emr/jobs/ your-configs/jobs/

# Edit configurations with your actual values
vim your-configs/endpoints/rds-mysql.endpoint.yaml
vim your-configs/endpoints/s3-data-lake.endpoint.yaml
vim your-configs/jobs/production-etl.yaml
```

### 2. Set Environment Variables

```bash
# Source your environment configuration
source your-environment-config.sh
```

### 3. Deploy to EMR

```bash
# Run the deployment script
./deploy-to-emr.sh
```

### 4. Monitor Execution

```bash
# Check step status
aws emr describe-step --cluster-id $EMR_CLUSTER_ID --step-id $STEP_ID

# View logs
aws emr describe-cluster --cluster-id $EMR_CLUSTER_ID
```

## Output Locations

### Parquet Files on S3

When using `ParquetWriterFormatConfig`, files are saved to:

```
s3://your-data-bucket/processed-data/customers/
├── year=2025/
│   └── month=01/
│       └── day=15/
│           ├── age_range=1/
│           │   └── part-00000-xxx.parquet
│           ├── age_range=2/
│           │   └── part-00000-xxx.parquet
│           └── age_range=3/
│               └── part-00000-xxx.parquet
└── _SUCCESS
```

### Database Tables

Processed data is written to:
- **Source → S3**: Raw data in Parquet format with partitioning
- **S3 → Data Warehouse**: Aggregated/transformed data in Redshift/RDS

### Control Table

Job execution metadata is tracked in:
```sql
SELECT * FROM job_logger;
-- Shows: job_id, system_name, table_name, record_count, bounds, timestamps
```

## Configuration Examples

### Parquet Output Configuration
```yaml
writer:
  type: "S3Writer"
  endpoint: "s3-data-lake.endpoint.yaml"
  key: "processed-data/customers/year=${YEAR}/month=${MONTH}/day=${DAY}/"
  mode: "append"
  num_files: 4
  partition_columns: ["age_range", "region"]
  format:
    type: "ParquetWriterFormatConfig"
    compression: "snappy"  # Options: snappy, gzip, lzo, brotli
```

### Delta Loading Configuration
```yaml
reader:
  type: "MysqlReader"
  endpoint: "rds-mysql.endpoint.yaml"
  table: "customers"
  strategy: "delta"
  delta_column: "updated_at"
  num_partitions: 8
```

## Security Best Practices

### 1. Use AWS Secrets Manager
```yaml
# In endpoint configuration
secrets_manager:
  enabled: true
  secret_name: "prod/database/credentials"
  region: "us-east-1"
```

### 2. IAM Roles for EMR
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:PutObject",
        "s3:ListBucket"
      ],
      "Resource": [
        "arn:aws:s3:::your-data-bucket/*",
        "arn:aws:s3:::your-config-bucket/*"
      ]
    },
    {
      "Effect": "Allow",
      "Action": [
        "secretsmanager:GetSecretValue"
      ],
      "Resource": "arn:aws:secretsmanager:*:*:secret:prod/*"
    }
  ]
}
```

### 3. Network Security
- Use VPC endpoints for S3 access
- Configure security groups for database access
- Enable encryption in transit and at rest

## Troubleshooting

### Common Issues

1. **Configuration not found**: Check S3 paths and permissions
2. **Database connection failed**: Verify security groups and credentials
3. **Parquet write errors**: Check S3 permissions and partition columns
4. **Memory issues**: Adjust EMR instance types and Spark configurations

### Debugging Commands

```bash
# Check EMR step logs
aws logs describe-log-streams --log-group-name /aws/emr/cluster/$EMR_CLUSTER_ID

# Validate S3 configurations
aws s3 ls s3://$S3_CONFIG_BUCKET/configs/endpoints/

# Test database connectivity
aws rds describe-db-instances --db-instance-identifier your-db-instance
```

## Performance Tuning

### EMR Configuration
```bash
# Optimize for large datasets
--conf spark.sql.adaptive.enabled=true
--conf spark.sql.adaptive.coalescePartitions.enabled=true
--conf spark.sql.adaptive.skewJoin.enabled=true

# Memory optimization
--conf spark.executor.memory=4g
--conf spark.executor.cores=2
--conf spark.executor.instances=10
```

### Parquet Optimization
```yaml
format:
  type: "ParquetWriterFormatConfig"
  compression: "snappy"  # Best balance of speed/compression
  # Consider "gzip" for better compression, "lzo" for faster processing
```

This setup provides a production-ready, secure, and scalable deployment for AWS EMR!

# Apache Iceberg Integration Guide

## Overview
Apache Iceberg has been successfully integrated as an output option for the Spark metadata-driven pipeline. This guide shows how to use Iceberg tables with Parquet storage format.

## What Was Added

### 1. Core Components
- **IcebergWriter**: Complete writer implementation with ACID transactions
- **Configuration Classes**: `IcebergWriterConfig` and `IcebergWriterFormatConfig`
- **Factory Integration**: `WriterFactory` now supports `IcebergWriter`
- **Spark Configuration**: Dedicated Iceberg catalog and extensions

### 2. Dependencies
- `iceberg-spark-runtime-3.5_2.12-1.4.3.jar`
- `iceberg-aws-1.4.3.jar`
- Located in: `src/main/resources/jars/`

### 3. Configuration Files
- **Job Config**: `src/main/resources/jobs/job.iceberg.sample.yaml`
- **Endpoint Config**: `src/main/resources/endpoints/iceberg.s3.endpoint.yaml`
- **Spark Config**: `src/main/config/environment.py` (Iceberg extensions)

## How to Run

### Prerequisites
1. LocalStack running on port 4566
2. MySQL and SQL Server databases set up
3. All JAR dependencies downloaded

### Execute Pipeline
```bash
cd src/main

# Set environment variables
export CONFIG_ROOT_DIR="resources"
export CONFIG_ENDPOINTS_DIR="resources/endpoints" 
export CONFIG_JOBS_DIR="resources/jobs"
export EXTRA_JARS_FOLDER="resources/jars"
export AWS_ACCESS_KEY_ID=test
export AWS_SECRET_ACCESS_KEY=test
export AWS_DEFAULT_REGION=us-east-1
export S3_ENDPOINT=http://localhost:4566

# Run pipeline
python3 pipeline_main.py \
  --config_file job.iceberg.sample.yaml \
  --config_source_type file \
  --config_root_dir resources \
  --app_type batch \
  --lowerbound 2023-01-01 \
  --upperbound 2023-12-31 \
  --report_date 2023-06-01 \
  --pipeline test \
  --batch_size 1000 \
  --custom '{}' \
  --predicate '' \
  --s3_type s3 \
  --job_id iceberg-job \
  --extra_jars_folder resources/jars
```

## Data Storage Locations

### Iceberg Warehouse
- **S3 Location**: `s3a://mock-bucket/iceberg-warehouse/`
- **Database**: `lakehouse`
- **Tables Created**:
  - `mysql_customers` (partitioned by age_range)
  - `mssql_customers` (partitioned by age_range)  
  - `s3_customers` (partitioned by data_source, age_range)

### Parquet Files
- **Format**: Parquet with Snappy compression
- **Location**: `{warehouse}/lakehouse/{table_name}/data/`
- **Managed by**: Iceberg metadata system

## Configuration Example

```yaml
writers:
  type: IcebergWriter
  endpoint: iceberg.s3.endpoint.yaml
  catalog_name: iceberg_catalog
  database: lakehouse
  table: my_table
  mode: append
  partition_columns:
    - partition_col
  table_properties:
    write.format.default: parquet
    write.parquet.compression-codec: snappy
  format:
    type: IcebergWriterFormat
    source_options:
      write.format.default: parquet
```

## Key Features

### ACID Transactions
- Atomic writes with automatic conflict resolution
- Consistent reads during concurrent operations

### Schema Evolution
- Automatic table creation with DataFrame schema
- Support for adding/modifying columns

### Time Travel
- Access to historical table snapshots
- Query previous versions of data

### Partitioning
- Support for partition columns
- Improved query performance

## Troubleshooting

### Common Issues
1. **JAR Loading**: Ensure all Iceberg JARs are in `resources/jars/`
2. **Catalog Configuration**: Use `iceberg_catalog` not `spark_catalog`
3. **S3 Endpoint**: Verify LocalStack is running and accessible

### Verification
Pipeline success is indicated by:
- No error messages during execution
- Job completion message
- Tables created in Iceberg warehouse

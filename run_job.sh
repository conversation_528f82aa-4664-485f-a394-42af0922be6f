#!/bin/bash

# Set AWS credentials for LocalStack
export AWS_ACCESS_KEY_ID=test
export AWS_SECRET_ACCESS_KEY=test
export AWS_DEFAULT_REGION=us-east-1

# Run the pipeline with LocalStack as S3 backend
python src/main/pipeline_main.py \
  --config_file job.sample.yaml \
  --config_source_type local \
  --config_root_dir src/main/resources \
  --app_type batch \
  --s3_type localstack \
  --s3_url http://localhost:4566 \
  --extra_jars_folder src/main/resources/jars
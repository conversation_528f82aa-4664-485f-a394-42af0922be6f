#!/bin/bash

# Spark Metadata-Driven Pipeline Environment Setup
# This script sets all required environment variables

echo "🔧 Setting up environment variables for Spark Metadata-Driven Pipeline..."

# Configuration paths (using local resources for development)
export CONFIG_ROOT_DIR="src/main/resources"
export CONFIG_ENDPOINTS_DIR="src/main/resources/endpoints"
export CONFIG_JOBS_DIR="src/main/resources/jobs"
export EXTRA_JARS_FOLDER="src/main/resources/jars"

# AWS credentials (for LocalStack development)
export AWS_ACCESS_KEY_ID="test"
export AWS_SECRET_ACCESS_KEY="test"
export AWS_DEFAULT_REGION="us-east-1"

# S3 endpoint (LocalStack for local development)
export S3_ENDPOINT="http://localhost:4566"

# Application settings
export APP_NAME_PREFIX="spark-metadata-pipeline"
export LOG_LEVEL="INFO"

# Additional Spark settings
export S3_PATH_STYLE_ACCESS="true"

echo "✅ Environment variables set successfully!"
echo ""
echo "📋 Current configuration:"
echo "  CONFIG_ROOT_DIR: $CONFIG_ROOT_DIR"
echo "  CONFIG_ENDPOINTS_DIR: $CONFIG_ENDPOINTS_DIR"
echo "  CONFIG_JOBS_DIR: $CONFIG_JOBS_DIR"
echo "  EXTRA_JARS_FOLDER: $EXTRA_JARS_FOLDER"
echo "  AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION"
echo "  S3_ENDPOINT: $S3_ENDPOINT"
echo ""
echo "🚀 You can now run the pipeline with:"
echo "  # Standard pipeline (PostgreSQL + S3)"
echo "  python3 src/main/pipeline_main.py --config_file job.sample.yaml --config_source_type local --config_root_dir src/main/resources --app_type batch --s3_type localstack --s3_url http://localhost:4566 --extra_jars_folder src/main/resources/jars"
echo ""
echo "  # Iceberg pipeline (Data Lake)"
echo "  python3 src/main/pipeline_main.py --config_file job.iceberg.sample.yaml --config_source_type local --config_root_dir src/main/resources --app_type batch --s3_type localstack --s3_url http://localhost:4566 --extra_jars_folder src/main/resources/jars"
